const AWS = require('aws-sdk');
const { getByIdCache, setCache } = require('./cache.util');

// Configure AWS SDK with retry logic
const eventBridge = new AWS.EventBridge({
    region: process.env.AWS_REGION,
    maxRetries: 3,
    retryDelayOptions: {
        customBackoff: function(retryCount) {
            return Math.pow(2, retryCount) * 100;
        }
    }
});

const RETRY_QUEUE_KEY = 'eventbridge_retry_queue';
const MAX_RETRY_ATTEMPTS = 3;

/**
 * Add a failed EventBridge operation to the retry queue
 */
async function addToRetryQueue(operation, partyId, attempt = 1) {
    try {
        const queue = await getByIdCache(RETRY_QUEUE_KEY) || [];
        
        const queueItem = {
            id: `${operation}_${partyId}_${Date.now()}`,
            operation,
            partyId,
            attempt,
            timestamp: new Date().toISOString(),
            nextRetry: new Date(Date.now() + (Math.pow(2, attempt) * 60000)).toISOString() // Exponential backoff in minutes
        };
        
        queue.push(queueItem);
        await setCache(RETRY_QUEUE_KEY, queue, 3600); // Cache for 1 hour
        
        console.log(`Added EventBridge operation to retry queue:`, queueItem);
    } catch (error) {
        console.error('Error adding to retry queue:', error);
    }
}

/**
 * Process the retry queue for failed EventBridge operations
 */
async function processRetryQueue() {
    try {
        const queue = await getByIdCache(RETRY_QUEUE_KEY) || [];
        const now = new Date();
        const processedItems = [];
        const remainingItems = [];
        
        for (const item of queue) {
            const nextRetryTime = new Date(item.nextRetry);
            
            if (now >= nextRetryTime && item.attempt <= MAX_RETRY_ATTEMPTS) {
                try {
                    console.log(`Retrying EventBridge operation:`, item);
                    
                    if (item.operation === 'scheduleParty') {
                        // You'll need to pass the party object here
                        // For now, we'll just log that we need to implement this
                        console.log(`Need to retry scheduling party ${item.partyId}`);
                        processedItems.push(item.id);
                    } else if (item.operation === 'deleteScheduledParty') {
                        console.log(`Need to retry deleting scheduled party ${item.partyId}`);
                        processedItems.push(item.id);
                    }
                    
                } catch (retryError) {
                    console.error(`Retry failed for ${item.id}:`, retryError);
                    
                    if (retryError.code === 'LimitExceededException' && item.attempt < MAX_RETRY_ATTEMPTS) {
                        // Re-queue with incremented attempt
                        const updatedItem = {
                            ...item,
                            attempt: item.attempt + 1,
                            nextRetry: new Date(Date.now() + (Math.pow(2, item.attempt + 1) * 60000)).toISOString()
                        };
                        remainingItems.push(updatedItem);
                    } else {
                        console.error(`Max retries exceeded or non-retryable error for ${item.id}`);
                        processedItems.push(item.id);
                    }
                }
            } else if (item.attempt > MAX_RETRY_ATTEMPTS) {
                console.error(`Max retries exceeded for ${item.id}, removing from queue`);
                processedItems.push(item.id);
            } else {
                // Not ready for retry yet
                remainingItems.push(item);
            }
        }
        
        // Update the queue with remaining items
        await setCache(RETRY_QUEUE_KEY, remainingItems, 3600);
        
        if (processedItems.length > 0) {
            console.log(`Processed ${processedItems.length} items from retry queue`);
        }
        
    } catch (error) {
        console.error('Error processing retry queue:', error);
    }
}

/**
 * Get current retry queue status
 */
async function getRetryQueueStatus() {
    try {
        const queue = await getByIdCache(RETRY_QUEUE_KEY) || [];
        return {
            totalItems: queue.length,
            items: queue.map(item => ({
                id: item.id,
                operation: item.operation,
                partyId: item.partyId,
                attempt: item.attempt,
                nextRetry: item.nextRetry
            }))
        };
    } catch (error) {
        console.error('Error getting retry queue status:', error);
        return { totalItems: 0, items: [] };
    }
}

module.exports = {
    addToRetryQueue,
    processRetryQueue,
    getRetryQueueStatus
};
