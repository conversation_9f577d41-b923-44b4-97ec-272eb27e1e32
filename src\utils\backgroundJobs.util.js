const { processRetryQueue, getRetryQueueStatus } = require('./eventBridgeQueue.util');

let retryQueueInterval = null;

/**
 * Start background job to process EventBridge retry queue
 */
function startBackgroundJobs() {
    console.log('Starting background jobs...');
    
    // Process retry queue every 2 minutes
    retryQueueInterval = setInterval(async () => {
        try {
            console.log('Processing EventBridge retry queue...');
            await processRetryQueue();
            
            // Log queue status
            const status = await getRetryQueueStatus();
            if (status.totalItems > 0) {
                console.log(`EventBridge retry queue status: ${status.totalItems} items pending`);
            }
        } catch (error) {
            console.error('Error in background job processing retry queue:', error);
        }
    }, 2 * 60 * 1000); // Every 2 minutes
    
    console.log('Background jobs started successfully');
}

/**
 * Stop background jobs
 */
function stopBackgroundJobs() {
    console.log('Stopping background jobs...');
    
    if (retryQueueInterval) {
        clearInterval(retryQueueInterval);
        retryQueueInterval = null;
    }
    
    console.log('Background jobs stopped');
}

/**
 * Get status of all background jobs
 */
async function getBackgroundJobsStatus() {
    const status = {
        retryQueueProcessor: {
            running: retryQueueInterval !== null,
            interval: '2 minutes'
        },
        retryQueue: await getRetryQueueStatus()
    };
    
    return status;
}

module.exports = {
    startBackgroundJobs,
    stopBackgroundJobs,
    getBackgroundJobsStatus
};
