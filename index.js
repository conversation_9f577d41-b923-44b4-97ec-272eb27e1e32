const express = require("express");
const path = require("path");
const rateLimit = require('express-rate-limit');

require("dotenv").config();

const typeDefs = require("./src/graphql/typeDefs");
const resolvers = require("./src/graphql/resolvers");
const webhookRouter = require("./src/routes/webhook");
const events = require("./src/routes/events");
const bodyParser = require('body-parser');
const { configureMiddleware, createServer, connectToDbAndRunApp } = require('./src/utils/serverSetup.util');

const startServer = async () => {
  const app = express();

  // Rate limiting configuration to prevent overwhelming AWS services
  const graphqlLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: '1 minute'
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  });

  // Apply rate limiting to GraphQL endpoint
  app.use('/graphql', graphqlLimiter);

  app.use(bodyParser.json());
  app.use("/webhook", webhookRouter);
  app.use("/activity_events", events);

  app.use(express.static(path.join(__dirname, 'public')));


  const server = await createServer(app, typeDefs, resolvers);

  // Middleware to set HTTP status code based on GraphQL response
  configureMiddleware(app);

  connectToDbAndRunApp(app, server);
};

startServer();